{"cells": [{"cell_type": "markdown", "id": "b8e2a3fb-cc4d-4f8d-8f78-97415e849416", "metadata": {}, "source": ["# Lesson 3: Preparing Text Data for RAG"]}, {"cell_type": "markdown", "id": "fafb0f88-0b26-4cdf-9b55-7ce191652155", "metadata": {}, "source": ["<p style=\"background-color:#fd4a6180; padding:15px; margin-left:20px\"> <b>Note:</b> This notebook takes about 30 seconds to be ready to use. Please wait until the \"Kernel starting, please wait...\" message clears from the top of the notebook before running any cells. You may start the video while you wait.</p>\n"]}, {"cell_type": "markdown", "id": "9fb43372-7cff-4dc6-8c47-2885aac4c09a", "metadata": {}, "source": ["### Import packages and set up Neo4j"]}, {"cell_type": "code", "execution_count": 6, "id": "e854e1a7-6c56-48e9-b3f0-999b68940726", "metadata": {"height": 149}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# Warning control\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 7, "id": "45e2bd85-1d15-4e91-9e6d-c1b647d8e97d", "metadata": {"height": 217}, "outputs": [], "source": ["# Load from environment\n", "load_dotenv('.env', override=True)\n", "NEO4J_URI = os.getenv('NEO4J_URI')\n", "NEO4J_USERNAME = os.getenv('NEO4J_USERNAME')\n", "NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD')\n", "NEO4J_DATABASE = os.getenv('NEO4J_DATABASE')\n", "OPENAI_API_KEY = os.getenv('OPENAI_API_KEY')\n", "\n", "# Note the code below is unique to this course environment, and not a \n", "# standard part of Neo4j's integration with OpenAI. Remove if running \n", "# in your own environment.\n", "OPENAI_ENDPOINT = os.getenv('OPENAI_BASE_URL') + '/embeddings'"]}, {"cell_type": "code", "execution_count": 8, "id": "0c277dd6-23b9-42d0-9d2a-c6aa9948dad9", "metadata": {"height": 81}, "outputs": [], "source": ["# Connect to the knowledge graph instance using LangChain\n", "kg = Neo4jGraph(\n", "    url=NEO4J_URI, username=NEO4J_USERNAME, password=NEO4J_PASSWORD, database=NEO4J_DATABASE\n", ")"]}, {"cell_type": "markdown", "id": "b4478433-1ab7-4ad6-ba51-73d54e45d798", "metadata": {}, "source": ["### Create a vector index "]}, {"cell_type": "code", "execution_count": 9, "id": "a26f80f6-d21a-447f-a5e2-eb940c0a25e4", "metadata": {"height": 166}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(\"\"\"\n", "  CREATE VECTOR INDEX movie_tagline_embeddings IF NOT EXISTS\n", "  FOR (m:Movie) ON (m.taglineEmbedding) \n", "  OPTIONS { indexConfig: {\n", "    `vector.dimensions`: 1536,\n", "    `vector.similarity_function`: 'cosine'\n", "  }}\"\"\"\n", ")\n"]}, {"cell_type": "code", "execution_count": 10, "id": "9472ede6-18da-4d13-a5de-0dbad618c0f3", "metadata": {"height": 81}, "outputs": [{"data": {"text/plain": ["[{'id': 3,\n", "  'name': 'movie_tagline_embeddings',\n", "  'state': 'ONLINE',\n", "  'populationPercent': 100.0,\n", "  'type': 'VECTOR',\n", "  'entityType': 'NODE',\n", "  'labelsOrTypes': ['Movie'],\n", "  'properties': ['taglineEmbedding'],\n", "  'indexProvider': 'vector-1.0',\n", "  'owningConstraint': None,\n", "  'lastRead': None,\n", "  'readCount': 0}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(\"\"\"\n", "  SHOW VECTOR INDEXES\n", "  \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "569049b5-6ad7-4348-81f8-068bde0f185c", "metadata": {"height": 30}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "ebe87478-723a-49e4-a197-b4cccd7153b0", "metadata": {}, "source": ["### Populate the vector index\n", "- Calculate vector representation for each movie tagline using OpenAI\n", "- Add vector to the `Movie` node as `taglineEmbedding` property"]}, {"cell_type": "code", "execution_count": 11, "id": "5921f75b-706d-414a-b7a0-b81623a71a23", "metadata": {"height": 217}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(\"\"\"\n", "    MATCH (movie:Movie) WHERE movie.tagline IS NOT NULL\n", "    WITH movie, genai.vector.encode(\n", "        movie.tagline, \n", "        \"OpenAI\", \n", "        {\n", "          token: $openAiApi<PERSON>ey,\n", "          endpoint: $openAiEndpoint\n", "        }) AS vector\n", "    CALL db.create.setNodeVectorProperty(movie, \"taglineEmbedding\", vector)\n", "    \"\"\", \n", "    params={\"openAiApiKey\":OPENAI_API_KEY, \"openAiEndpoint\": OPENAI_ENDPOINT} )"]}, {"cell_type": "code", "execution_count": 12, "id": "73efe0c6-d3e9-4815-9154-04f177101f17", "metadata": {"height": 132}, "outputs": [], "source": ["result = kg.query(\"\"\"\n", "    MATCH (m:Movie) \n", "    WHERE m.tagline IS NOT NULL\n", "    RETURN m.tagline, m.taglineEmbedding\n", "    LIMIT 1\n", "    \"\"\"\n", ")"]}, {"cell_type": "code", "execution_count": 13, "id": "71f4fe0b-01da-493f-862b-36a60842d648", "metadata": {"height": 30}, "outputs": [{"data": {"text/plain": ["'Welcome to the Real World'"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["result[0]['m.tagline']"]}, {"cell_type": "code", "execution_count": 14, "id": "93c3fe17-7310-4e11-84c3-4955c8ed8f1a", "metadata": {"height": 30}, "outputs": [{"data": {"text/plain": ["[0.01746697723865509,\n", " -0.005447783973067999,\n", " -0.0020621647126972675,\n", " -0.025564946234226227,\n", " -0.014322134666144848,\n", " 0.016733180731534958,\n", " -0.017047664150595665,\n", " 0.0004647650639526546,\n", " -0.025211151689291,\n", " -0.029509101063013077]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["result[0]['m.taglineEmbedding'][:10]"]}, {"cell_type": "code", "execution_count": 15, "id": "221af96b-63ce-4a95-8d23-47f9aa8c3a4e", "metadata": {"height": 30}, "outputs": [{"data": {"text/plain": ["1536"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["len(result[0]['m.taglineEmbedding'])"]}, {"cell_type": "code", "execution_count": null, "id": "03365be1-4aaa-4440-bbc5-0df650fd9013", "metadata": {"height": 30}, "outputs": [], "source": []}, {"cell_type": "markdown", "id": "790b238d-9189-4111-962b-1bf3dfcdad05", "metadata": {}, "source": ["### Similarity search\n", "- Calculate embedding for question\n", "- Identify matching movies based on similarity of question and `taglineEmbedding` vectors"]}, {"cell_type": "code", "execution_count": 16, "id": "5648d30a-ab89-4fa1-ad61-35bbb43ef102", "metadata": {"height": 30}, "outputs": [], "source": ["question = \"What movies are about love?\""]}, {"cell_type": "code", "execution_count": 17, "id": "6b1bc582-fd8e-4a2f-8347-fb62cbfec1c2", "metadata": {"height": 353}, "outputs": [{"data": {"text/plain": ["[{'movie.title': '<PERSON> Versus the Volcano',\n", "  'movie.tagline': 'A story of love, lava and burning desire.',\n", "  'score': 0.9062913656234741},\n", " {'movie.title': 'As Good as It Gets',\n", "  'movie.tagline': 'A comedy from the heart that goes for the throat.',\n", "  'score': 0.9022705554962158},\n", " {'movie.title': 'Snow Falling on Cedars',\n", "  'movie.tagline': 'First loves last. Forever.',\n", "  'score': 0.9013131856918335},\n", " {'movie.title': 'Sleepless in Seattle',\n", "  'movie.tagline': 'What if someone you never met, someone you never saw, someone you never knew was the only someone for you?',\n", "  'score': 0.8945093154907227},\n", " {'movie.title': 'When Harry Met Sally',\n", "  'movie.tagline': 'Can two friends sleep together and still love each other in the morning?',\n", "  'score': 0.894220232963562}]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(\"\"\"\n", "    WITH genai.vector.encode(\n", "        $question, \n", "        \"OpenAI\", \n", "        {\n", "          token: $openAiApi<PERSON>ey,\n", "          endpoint: $openAiEndpoint\n", "        }) AS question_embedding\n", "    CALL db.index.vector.queryNodes(\n", "        'movie_tagline_embeddings', \n", "        $top_k, \n", "        question_embedding\n", "        ) YIELD node AS movie, score\n", "    RETURN movie.title, movie.tagline, score\n", "    \"\"\", \n", "    params={\"openAiApiKey\":OPENAI_API_KEY,\n", "            \"openAiEndpoint\": OPENAI_ENDPOINT,\n", "            \"question\": question,\n", "            \"top_k\": 5\n", "            })"]}, {"cell_type": "markdown", "id": "0d3c6eaa-aaea-4a03-b9f8-3c0ead98e83b", "metadata": {}, "source": ["### Try for yourself: ask you own question!\n", "- Change the question below and run the graph query to find different movies"]}, {"cell_type": "code", "execution_count": 20, "id": "21b08047-0086-4291-8d62-fb646de330fa", "metadata": {"height": 30}, "outputs": [], "source": ["question = \"What movies are about life?\""]}, {"cell_type": "code", "execution_count": 21, "id": "7cdc7751-dddf-46b8-a74b-3d7c5544748f", "metadata": {"height": 285}, "outputs": [{"data": {"text/plain": ["[{'movie.title': 'As Good as It Gets',\n", "  'movie.tagline': 'A comedy from the heart that goes for the throat.',\n", "  'score': 0.9046510457992554},\n", " {'movie.title': 'RescueDawn',\n", "  'movie.tagline': \"Based on the extraordinary true story of one man's fight for freedom\",\n", "  'score': 0.8957586884498596},\n", " {'movie.title': 'That Thing You Do',\n", "  'movie.tagline': 'In every life there comes a time when that thing you dream becomes that thing you do',\n", "  'score': 0.8943912982940674},\n", " {'movie.title': 'What Dreams May Come',\n", "  'movie.tagline': 'After life there is more. The end is just the beginning.',\n", "  'score': 0.8910602927207947},\n", " {'movie.title': 'Twister',\n", "  'movie.tagline': \"Don't Breathe. Don't Look Back.\",\n", "  'score': 0.8904589414596558}]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(\"\"\"\n", "    WITH genai.vector.encode(\n", "        $question, \n", "        \"OpenAI\", \n", "        {\n", "          token: $openAiApi<PERSON>ey,\n", "          endpoint: $openAiEndpoint\n", "        }) AS question_embedding\n", "    CALL db.index.vector.queryNodes(\n", "        'movie_tagline_embeddings', \n", "        $top_k, \n", "        question_embedding\n", "        ) YIELD node AS movie, score\n", "    RETURN movie.title, movie.tagline, score\n", "    \"\"\", \n", "    params={\"openAiApiKey\":OPENAI_API_KEY,\n", "            \"openAiEndpoint\": OPENAI_ENDPOINT,\n", "            \"question\": question,\n", "            \"top_k\": 5\n", "            })"]}, {"cell_type": "code", "execution_count": null, "id": "ca79a456", "metadata": {"height": 30}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}