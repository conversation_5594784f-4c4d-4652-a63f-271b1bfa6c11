{"cells": [{"cell_type": "markdown", "id": "1", "metadata": {}, "source": ["# Building Knowledge Graph for Maize Toy Data\n", "\n", "This notebook demonstrates how to build a knowledge graph from the maize.csv toy data using Neo4j."]}, {"cell_type": "markdown", "id": "2", "metadata": {}, "source": ["## Setup and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "3", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "from dotenv import load_dotenv\n", "from neo4j import GraphDatabase\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "class Neo4jConnection:\n", "    \"\"\"Neo4j database connection wrapper\"\"\"\n", "    \n", "    def __init__(self, uri, username, password, database=\"neo4j\"):\n", "        self.driver = GraphDatabase.driver(uri, auth=(username, password))\n", "        self.database = database\n", "    \n", "    def close(self):\n", "        if self.driver:\n", "            self.driver.close()\n", "    \n", "    def query(self, cypher, params=None):\n", "        \"\"\"Execute a Cypher query and return results\"\"\"\n", "        with self.driver.session(database=self.database) as session:\n", "            result = session.run(cypher, params or {})\n", "            return [record.data() for record in result]"]}, {"cell_type": "markdown", "id": "4", "metadata": {}, "source": ["## Load and Examine the Data"]}, {"cell_type": "code", "execution_count": null, "id": "5", "metadata": {}, "outputs": [], "source": ["# Load the maize data\n", "df = pd.read_csv('../toydata/maize.csv')\n", "print(f\"Dataset shape: {df.shape}\")\n", "print(\"\\nFirst few rows:\")\n", "df.head()"]}, {"cell_type": "code", "execution_count": null, "id": "6", "metadata": {}, "outputs": [], "source": ["# Examine the data structure\n", "print(\"Unique subjects:\")\n", "print(df['subject'].unique())\n", "print(\"\\nUnique predicates:\")\n", "print(df['predicate'].unique())\n", "print(\"\\nUnique objects:\")\n", "print(df['object'].unique())"]}, {"cell_type": "markdown", "id": "7", "metadata": {}, "source": ["## Connect to Neo4j"]}, {"cell_type": "code", "execution_count": null, "id": "8", "metadata": {}, "outputs": [], "source": ["# Load environment variables\n", "load_dotenv('../.env', override=True)\n", "\n", "NEO4J_URI = os.getenv('NEO4J_URI', 'bolt://localhost:7687')\n", "NEO4J_USERNAME = os.getenv('NEO4J_USERNAME', 'neo4j')\n", "NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD')\n", "NEO4J_DATABASE = os.getenv('NEO4J_DATABASE', 'neo4j')\n", "\n", "print(f\"Connecting to Neo4j at: {NEO4J_URI}\")\n", "\n", "# Initialize Neo4j connection\n", "kg = Neo4jConnection(\n", "    uri=NEO4J_URI, \n", "    username=NEO4J_USERNAME, \n", "    password=NEO4J_PASSWORD, \n", "    database=NEO4J_DATABASE\n", ")\n", "\n", "# Test connection\n", "try:\n", "    result = kg.query(\"RETURN 1 as test\")\n", "    print(\"Connected successfully!\")\n", "except Exception as e:\n", "    print(f\"Connection failed: {e}\")"]}, {"cell_type": "markdown", "id": "9", "metadata": {}, "source": ["## Build the Knowledge Graph"]}, {"cell_type": "code", "execution_count": null, "id": "10", "metadata": {}, "outputs": [], "source": ["# Run the build script\n", "exec(open('../build_maize_kg.py').read())"]}, {"cell_type": "markdown", "id": "11", "metadata": {}, "source": ["## Query the Knowledge Graph"]}, {"cell_type": "code", "execution_count": null, "id": "12", "metadata": {}, "outputs": [], "source": ["# Count all nodes\n", "result = kg.query(\"MATCH (n) RETURN count(n) as total_nodes\")\n", "print(f\"Total nodes in graph: {result[0]['total_nodes']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "13", "metadata": {}, "outputs": [], "source": ["# Find all genes and what they regulate\n", "cypher = \"\"\"\n", "MATCH (g:<PERSON>)-[:REGULATES]->(t:Trait)\n", "RETURN g.name as gene, t.name as trait\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Genes and their regulated traits:\")\n", "for row in result:\n", "    print(f\"  {row['gene']} → {row['trait']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "14", "metadata": {}, "outputs": [], "source": ["# Find genotypes and their traits\n", "cypher = \"\"\"\n", "MATCH (gt:Genotype)-[:HAS_TRAIT]->(t:Trait)\n", "RETURN gt.name as genotype, t.name as trait\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Genotypes and their traits:\")\n", "for row in result:\n", "    print(f\"  {row['genotype']} → {row['trait']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "15", "metadata": {}, "outputs": [], "source": ["# Find complex relationships: genes → traits ← genotypes\n", "cypher = \"\"\"\n", "MATCH (g:<PERSON>)-[:REGULATES]->(t:Trait)<-[:HAS_TRAIT]-(gt:Genotype)\n", "RETURN g.name as gene, t.name as trait, gt.name as genotype\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Gene-Trait-Genotype connections:\")\n", "for row in result:\n", "    print(f\"  {row['gene']} regulates {row['trait']} found in {row['genotype']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "16", "metadata": {}, "outputs": [], "source": ["# Find trial information\n", "cypher = \"\"\"\n", "MATCH (trial:Trial)-[:CONDUCTED_IN]->(loc:Location)\n", "MATCH (gt:Genotype)-[:TESTED_IN]->(trial)\n", "MATCH (trial)-[:MEASURED]->(trait:Trait)\n", "RETURN trial.name as trial, loc.name as location, \n", "       collect(DISTINCT gt.name) as genotypes_tested,\n", "       collect(DISTINCT trait.name) as traits_measured\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Trial information:\")\n", "for row in result:\n", "    print(f\"  Trial: {row['trial']}\")\n", "    print(f\"    Location: {row['location']}\")\n", "    print(f\"    Genotypes tested: {', '.join(row['genotypes_tested'])}\")\n", "    print(f\"    Traits measured: {', '.join(row['traits_measured'])}\")"]}, {"cell_type": "markdown", "id": "17", "metadata": {}, "source": ["## Advanced Queries"]}, {"cell_type": "code", "execution_count": null, "id": "18", "metadata": {}, "outputs": [], "source": ["# Find the path from gene to QTL through trait\n", "cypher = \"\"\"\n", "MATCH path = (g:<PERSON>)-[:REGULATES]->(t:Trait)-[:ASSOCIATED_WITH]->(q:QTL)\n", "RETURN g.name as gene, t.name as trait, q.name as qtl\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Gene → Trait → QTL pathways:\")\n", "for row in result:\n", "    print(f\"  {row['gene']} → {row['trait']} → {row['qtl']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "19", "metadata": {}, "outputs": [], "source": ["# Find QTL chromosomal locations\n", "cypher = \"\"\"\n", "MATCH (q:QTL)-[:LOCATED_ON]->(c:Chromosome)\n", "RETURN q.name as qtl, c.name as chromosome\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"QTL chromosomal locations:\")\n", "for row in result:\n", "    print(f\"  {row['qtl']} is located on {row['chromosome']}\")"]}, {"cell_type": "code", "execution_count": null, "id": "20", "metadata": {}, "outputs": [], "source": ["# Environmental context\n", "cypher = \"\"\"\n", "MATCH (loc:Location)-[:HAS_WEATHER]->(w:Weather)\n", "RETURN loc.name as location, w.name as weather\n", "\"\"\"\n", "result = kg.query(cypher)\n", "print(\"Environmental conditions:\")\n", "for row in result:\n", "    print(f\"  {row['location']} has {row['weather']} conditions\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 5}