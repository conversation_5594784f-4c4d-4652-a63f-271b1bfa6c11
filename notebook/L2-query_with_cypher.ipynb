{"cells": [{"cell_type": "markdown", "id": "0151deab-ec4e-4738-b9b0-fec2424b2da3", "metadata": {}, "source": ["# Lesson 2: Querying Knowledge Graphs with <PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "7dc06c11-a750-4c86-95bb-0cfde30a2ecb", "metadata": {}, "source": ["<p style=\"background-color:#fd4a6180; padding:15px; margin-left:20px\"> <b>Note:</b> This notebook takes about 30 seconds to be ready to use. Please wait until the \"Kernel starting, please wait...\" message clears from the top of the notebook before running any cells. You may start the video while you wait.</p>\n"]}, {"cell_type": "markdown", "id": "74b00a43-df0b-4ead-8de6-2365e8a26f53", "metadata": {}, "source": ["### Import packages and set up Neo4"]}, {"cell_type": "code", "execution_count": 1, "id": "bc780563-88e7-4c75-92c7-eb34def44a9f", "metadata": {"height": 149}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "import os\n", "\n", "from langchain_community.graphs import Neo4jGraph\n", "\n", "# Warning control\n", "import warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": 2, "id": "186948fd-da5e-49d5-abf4-ad21aed3271f", "metadata": {"height": 98}, "outputs": [], "source": ["load_dotenv('.env', override=True)\n", "NEO4J_URI = os.getenv('NEO4J_URI')\n", "NEO4J_USERNAME = os.getenv('NEO4J_USERNAME')\n", "NEO4J_PASSWORD = os.getenv('NEO4J_PASSWORD')\n", "NEO4J_DATABASE = os.getenv('NEO4J_DATABASE')"]}, {"cell_type": "markdown", "id": "7a8affe7-9424-47d4-97c7-0a675fc2398f", "metadata": {}, "source": ["- Initialize a knowledge graph instance using <PERSON><PERSON><PERSON><PERSON>'s Neo4j integration"]}, {"cell_type": "code", "execution_count": 3, "id": "a5c03340-9078-42b2-9e0a-2dc8495b818f", "metadata": {"height": 64}, "outputs": [], "source": ["kg = Neo4jGraph(\n", "    url=NEO4J_URI, username=NEO4J_USERNAME, password=NEO4J_PASSWORD, database=NEO4J_DATABASE\n", ")"]}, {"cell_type": "markdown", "id": "a703052f-c74e-4933-8e62-7e3ec9b12986", "metadata": {}, "source": ["### Querying the movie knowledge graph \n", "- Match all nodes in the graph"]}, {"cell_type": "code", "execution_count": 4, "id": "b12cfece-6188-4fa4-8ba6-f0eff39c6546", "metadata": {"height": 81, "scrolled": true}, "outputs": [], "source": ["cypher = \"\"\"\n", "  MATCH (n) \n", "  RETURN count(n)\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": 5, "id": "edef9911-c436-4159-ae6b-412557448b94", "metadata": {"height": 47, "scrolled": true}, "outputs": [{"data": {"text/plain": ["[{'count(n)': 171}]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result = kg.query(cypher)\n", "result"]}, {"cell_type": "code", "execution_count": 6, "id": "751a9532-114d-4f72-8956-da41aa7eae8d", "metadata": {"height": 81}, "outputs": [], "source": ["cypher = \"\"\"\n", "  MATCH (n) \n", "  RETURN count(n) AS numberOfNodes\n", "  \"\"\""]}, {"cell_type": "code", "execution_count": 7, "id": "4fd44fea-11ab-4828-b475-71bc450ee900", "metadata": {"height": 47}, "outputs": [{"data": {"text/plain": ["[{'numberOfNodes': 171}]"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["result = kg.query(cypher)\n", "result"]}, {"cell_type": "code", "execution_count": 8, "id": "4ae04daa-bc4c-42ca-864a-79b3915a234e", "metadata": {"height": 30}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["There are 171 nodes in this graph.\n"]}], "source": ["print(f\"There are {result[0]['numberOfNodes']} nodes in this graph.\")"]}, {"cell_type": "markdown", "id": "ef33d57e-7f3a-4d66-b4d0-325973604906", "metadata": {}, "source": ["- Match only the `Movie` nodes by specifying the node label"]}, {"cell_type": "code", "execution_count": 9, "id": "7ebd1092-e6d4-4112-aac9-ce2af1eaa737", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'numberOfMovies': 38}]"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (n:Movie) \n", "  RETURN count(n) AS numberOfMovies\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "78ca792e-831f-4f1a-b728-92928d41e9b6", "metadata": {}, "source": ["- Change the variable name in the node pattern match for improved readability"]}, {"cell_type": "code", "execution_count": 10, "id": "26b1ed1f-c780-4de8-8c96-5594bc8b63af", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'numberOfMovies': 38}]"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (m:Movie) \n", "  RETURN count(m) AS numberOfMovies\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "b0534a64-f4c0-4e22-a79d-cce5ef595aa3", "metadata": {}, "source": ["- Match only the `Person` nodes"]}, {"cell_type": "code", "execution_count": 11, "id": "5b7d6465-19a7-4f0c-b2a3-f32fd5991c46", "metadata": {"height": 98, "scrolled": true}, "outputs": [{"data": {"text/plain": ["[{'numberOfPeople': 133}]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (people:Person) \n", "  RETURN count(people) AS numberOfPeople\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "209a950f-3335-4a47-801c-53a821529cc7", "metadata": {}, "source": ["- Match a single person by specifying the value of the `name` property on the `Person` node"]}, {"cell_type": "code", "execution_count": 12, "id": "1c1f5e93-39dc-4585-ba3a-2cd492cbf665", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'tom': {'born': 1956, 'name': '<PERSON>'}}]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (tom:Person {name:\"<PERSON>\"}) \n", "  RETURN tom\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "936ee283-90ba-4ada-92b2-dd7c6bccfdc9", "metadata": {}, "source": ["- Match a single `Movie` by specifying the value of the `title` property"]}, {"cell_type": "code", "execution_count": 13, "id": "d952bf0c-d2c0-4b64-86b5-29e2521f543e", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'cloudAtlas': {'tagline': 'Everything is connected',\n", "   'title': 'Cloud Atlas',\n", "   'released': 2012}}]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (cloudAtlas:Movie {title:\"Cloud Atlas\"}) \n", "  RETURN cloudAtlas\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "3f6debaf-f237-4b75-b18c-5ff56ccca780", "metadata": {}, "source": ["- Return only the `released` property of the matched `Movie` node"]}, {"cell_type": "code", "execution_count": 14, "id": "2c9d8fc2-4fc7-4e85-8ced-e3f0236e6b2f", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'cloudAtlas.released': 2012}]"]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (cloudAtlas:Movie {title:\"Cloud Atlas\"}) \n", "  RETURN cloudAtlas.released\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "00e73a39-882c-4060-926e-deb484a4358f", "metadata": {}, "source": ["- Return two properties"]}, {"cell_type": "code", "execution_count": 15, "id": "64ad1276-c1cc-49d6-b7e9-d7126a37fabc", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'cloudAtlas.released': 2012,\n", "  'cloudAtlas.tagline': 'Everything is connected'}]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (cloudAtlas:Movie {title:\"Cloud Atlas\"}) \n", "  RETURN cloudAtlas.released, cloudAtlas.tagline\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "689be729-367a-408b-b4e5-43d2a3d69744", "metadata": {}, "source": ["### Cypher patterns with conditional matching"]}, {"cell_type": "code", "execution_count": 16, "id": "fc7b4d4d-764e-4b83-ae7c-0f7717de85f2", "metadata": {"height": 132}, "outputs": [], "source": ["cypher = \"\"\"\n", "  MATCH (nineties:Movie) \n", "  WHERE nineties.released >= 1990 \n", "    AND nineties.released < 2000 \n", "  RETURN nineties.title\n", "  \"\"\"\n"]}, {"cell_type": "code", "execution_count": 17, "id": "c52524e8-c4b2-443a-9e4b-01c50f0be352", "metadata": {"height": 30}, "outputs": [{"data": {"text/plain": ["[{'nineties.title': 'The Matrix'},\n", " {'nineties.title': \"The Devil's Advocate\"},\n", " {'nineties.title': 'A Few Good Men'},\n", " {'nineties.title': 'As Good as It Gets'},\n", " {'nineties.title': 'What Dreams May Come'},\n", " {'nineties.title': 'Snow Falling on Cedars'},\n", " {'nineties.title': \"You've Got Mail\"},\n", " {'nineties.title': 'Sleepless in Seattle'},\n", " {'nineties.title': '<PERSON>us the Volcano'},\n", " {'nineties.title': 'When <PERSON> Met <PERSON>'},\n", " {'nineties.title': 'That Thing You Do'},\n", " {'nineties.title': 'The Birdcage'},\n", " {'nineties.title': 'Unforgiven'},\n", " {'nineties.title': '<PERSON>'},\n", " {'nineties.title': 'The Green Mile'},\n", " {'nineties.title': '<PERSON><PERSON><PERSON>'},\n", " {'nineties.title': 'Apollo 13'},\n", " {'nineties.title': 'Twister'},\n", " {'nineties.title': 'Bicentennial Man'},\n", " {'nineties.title': 'A League of Their Own'}]"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["kg.query(cypher)"]}, {"cell_type": "markdown", "id": "d176b9d2-ec33-423a-b3de-e9bc87810fe7", "metadata": {}, "source": ["### Pattern matching with multiple nodes"]}, {"cell_type": "code", "execution_count": 18, "id": "dd8ae45b-c131-4e39-a937-704855cbdd74", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'actor.name': '<PERSON>', 'movie.title': 'The Matrix'},\n", " {'actor.name': '<PERSON>', 'movie.title': 'The Matrix'},\n", " {'actor.name': '<PERSON>', 'movie.title': 'The Matrix'},\n", " {'actor.name': '<PERSON><PERSON><PERSON>', 'movie.title': 'The Matrix'},\n", " {'actor.name': '<PERSON><PERSON>', 'movie.title': 'The Matrix'},\n", " {'actor.name': '<PERSON>', 'movie.title': 'The Matrix Reloaded'},\n", " {'actor.name': '<PERSON>', 'movie.title': 'The Matrix Reloaded'},\n", " {'actor.name': '<PERSON><PERSON><PERSON>', 'movie.title': 'The Matrix Reloaded'},\n", " {'actor.name': '<PERSON><PERSON>', 'movie.title': 'The Matrix Reloaded'},\n", " {'actor.name': '<PERSON>', 'movie.title': 'The Matrix Revolutions'}]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (actor:Person)-[:ACTED_IN]->(movie:Movie) \n", "  RETURN actor.name, movie.title LIMIT 10\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "code", "execution_count": 19, "id": "e79dd7e8-a778-467e-b6d9-ba6af8eee5bf", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'tom.name': '<PERSON>', 'tomHanksMovies.title': 'Apollo 13'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': \"You've Got Mail\"},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'A League of Their Own'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': '<PERSON> the Volcano'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'That Thing You Do'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'The Da Vinci Code'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'Cloud Atlas'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'Cast Away'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'The Green Mile'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'Sleepless in Seattle'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': 'The Polar Express'},\n", " {'tom.name': '<PERSON>', 'tomHanksMovies.title': \"<PERSON>'s War\"}]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (tom:Person {name: \"<PERSON>\"})-[:ACTED_IN]->(tomHanksMovies:Movie) \n", "  RETURN tom.name,tomHanksMovies.title\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "code", "execution_count": 20, "id": "004147cc-14f4-43eb-86ff-d22e11a1d0d2", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'coActors.name': '<PERSON>', 'm.title': 'Apollo 13'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Apollo 13'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Apollo 13'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Apollo 13'},\n", " {'coActors.name': '<PERSON>', 'm.title': \"You've Got Mail\"},\n", " {'coActors.name': '<PERSON>', 'm.title': \"You've Got Mail\"},\n", " {'coActors.name': '<PERSON>', 'm.title': \"You've Got Mail\"},\n", " {'coActors.name': '<PERSON>', 'm.title': \"You've Got Mail\"},\n", " {'coActors.name': '<PERSON>', 'm.title': \"You've Got Mail\"},\n", " {'coActors.name': '<PERSON>', 'm.title': 'A League of Their Own'},\n", " {'coActors.name': \"<PERSON>\", 'm.title': 'A League of Their Own'},\n", " {'coActors.name': '<PERSON><PERSON>', 'm.title': 'A League of Their Own'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'A League of Their Own'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'A League of Their Own'},\n", " {'coActors.name': '<PERSON>', 'm.title': '<PERSON> the Volcano'},\n", " {'coActors.name': '<PERSON>', 'm.title': '<PERSON> the Volcano'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'That Thing You Do'},\n", " {'coActors.name': '<PERSON><PERSON><PERSON>', 'm.title': 'That Thing You Do'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Da Vinci Code'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Da Vinci Code'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Da Vinci Code'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Cloud Atlas'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Cloud Atlas'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Cloud Atlas'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Cast Away'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'The Green Mile'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Sleepless in Seattle'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Sleepless in Seattle'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Sleepless in Seattle'},\n", " {'coActors.name': '<PERSON>', 'm.title': 'Sleepless in Seattle'},\n", " {'coActors.name': \"<PERSON>\", 'm.title': 'Sleepless in Seattle'},\n", " {'coActors.name': '<PERSON>', 'm.title': \"<PERSON>'s War\"},\n", " {'coActors.name': '<PERSON>',\n", "  'm.title': \"<PERSON>'s War\"}]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "  MATCH (tom:Person {name:\"<PERSON>\"})-[:ACTED_IN]->(m)<-[:ACTED_IN]-(coActors) \n", "  RETURN coActors.name, m.title\n", "  \"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "2ad3f683-d334-4c4e-b274-9789a70b9662", "metadata": {}, "source": ["### Delete data from the graph"]}, {"cell_type": "code", "execution_count": 21, "id": "5064cab8-b5d6-4f1c-b5ef-f154f5c10b2b", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[{'emil.name': '<PERSON>', 'movie.title': 'The Matrix'}]"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "MATCH (emil:Person {name:\"<PERSON>\"})-[actedIn:ACTED_IN]->(movie:Movie)\n", "RETURN emil.name, movie.title\n", "\"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "code", "execution_count": 22, "id": "648d12d1-0966-46ff-ad77-080eae553c3a", "metadata": {"height": 98}, "outputs": [{"data": {"text/plain": ["[]"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "MATCH (emil:Person {name:\"<PERSON>\"})-[actedIn:ACTED_IN]->(movie:Movie)\n", "DELETE actedIn\n", "\"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "markdown", "id": "109de94e-3526-4dad-9a62-1beb6c5dad2d", "metadata": {}, "source": ["### Adding data to the graph"]}, {"cell_type": "code", "execution_count": 23, "id": "ed1665da-b9ec-4b81-9e1f-de19af49d0d8", "metadata": {"height": 115}, "outputs": [{"data": {"text/plain": ["[{'andreas': {'name': '<PERSON>'}}]"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "CREATE (andreas:Person {name:\"<PERSON>\"})\n", "RETURN andreas\n", "\"\"\"\n", "\n", "kg.query(cypher)"]}, {"cell_type": "code", "execution_count": 24, "id": "1a0b9bb9-4491-45e2-9a4d-2306ea0a2bd8", "metadata": {"height": 115}, "outputs": [{"data": {"text/plain": ["[{'andreas': {'name': '<PERSON>'},\n", "  'hasRelationship': ({'name': '<PERSON>'},\n", "   'WORKS_WITH',\n", "   {'born': 1978, 'name': '<PERSON>'}),\n", "  'emil': {'born': 1978, 'name': '<PERSON>'}}]"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["cypher = \"\"\"\n", "MATCH (andreas:Person {name:\"<PERSON>\"}), (emil:Person {name:\"<PERSON>\"})\n", "MERGE (andreas)-[hasRelationship:WORKS_WITH]->(emil)\n", "RETURN andreas, hasRelationship, emil\n", "\"\"\"\n", "kg.query(cypher)"]}, {"cell_type": "code", "execution_count": null, "id": "6b31eb1e", "metadata": {"height": 30}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}