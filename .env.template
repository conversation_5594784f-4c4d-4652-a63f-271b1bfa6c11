# Neo4j Configuration
# Copy this file to .env and update with your Neo4j credentials

# Neo4j connection details
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your_password_here
NEO4J_DATABASE=neo4j

# For Neo4j AuraDB (cloud), use something like:
# NEO4J_URI=neo4j+s://your-instance.databases.neo4j.io
# NEO4J_USERNAME=neo4j
# NEO4J_PASSWORD=your_aura_password
# NEO4J_DATABASE=neo4j
